# Serverless Framework configuration file for Biormika HFO (High Frequency Oscillation) backend
service: biormika-hfo-backend

# Specify Serverless Framework version
frameworkVersion: '3'

# Cloud provider configuration
provider:
  name: aws                                    # Deploy to Amazon Web Services
  runtime: python3.9                          # Runtime for local development (container defines runtime for AWS)
  stage: ${opt:stage, 'dev'}                  # Deployment stage (dev/prod), defaults to 'dev'
  region: ${opt:region, 'us-east-1'}          # AWS region, defaults to us-east-1
  ecr:                                        # ECR configuration for container images
    images:
      biormika-lambda:                        # Image name referenced in functions
        path: ./                              # Path to Dockerfile
  environment:                                # Environment variables for all Lambda functions
    STAGE: ${self:provider.stage}             # Current deployment stage
    APP_REGION: ${self:provider.region}       # Application region (AWS_REGION is reserved)
    S3_BUCKET: ${self:custom.s3Bucket}        # S3 bucket name for file storage
    COGNITO_USER_POOL_ID:                     # Cognito User Pool ID for authentication
      Ref: CognitoUserPool                    # Reference to CloudFormation resource
    COGNITO_CLIENT_ID:                        # Cognito Client ID for app authentication
      Ref: CognitoUserPoolClient              # Reference to CloudFormation resource
    DYNAMODB_TABLE: ${self:service}-${self:provider.stage}  # DynamoDB table name
    STATE_MACHINE_ARN: !Sub 'arn:aws:states:${AWS::Region}:${AWS::AccountId}:stateMachine:${self:service}-${self:provider.stage}-analysis'  # Step Functions state machine ARN
  iam:                                        # IAM permissions for Lambda functions
    role:
      statements:
        - Effect: Allow                       # Allow S3 object operations
          Action:
            - s3:GetObject                    # Read files from S3
            - s3:PutObject                    # Upload files to S3
            - s3:DeleteObject                 # Delete files from S3
          Resource:
            - arn:aws:s3:::${self:custom.s3Bucket}/*  # Apply to all objects in bucket
        - Effect: Allow                       # Allow S3 bucket operations
          Action:
            - s3:CreateBucket                 # Create the S3 bucket
            - s3:ListBucket                   # List bucket contents
          Resource:
            - arn:aws:s3:::${self:custom.s3Bucket}    # Apply to the bucket itself
        - Effect: Allow                       # Allow DynamoDB operations
          Action:
            - dynamodb:Query                  # Query DynamoDB table
            - dynamodb:Scan                   # Scan DynamoDB table
            - dynamodb:GetItem                # Get single item from DynamoDB
            - dynamodb:PutItem                # Insert item into DynamoDB
            - dynamodb:UpdateItem             # Update item in DynamoDB
            - dynamodb:DeleteItem             # Delete item from DynamoDB
          Resource:
            - "Fn::GetAtt": [DynamoDBTable, Arn]     # Reference to DynamoDB table ARN
        - Effect: Allow                       # Allow Cognito admin operations
          Action:
            - cognito-idp:AdminInitiateAuth   # Initiate authentication flow
            - cognito-idp:AdminCreateUser     # Create new user
            - cognito-idp:AdminSetUserPassword # Set user password
            - cognito-idp:AdminGetUser        # Get user details
          Resource:
            - "Fn::GetAtt": [CognitoUserPool, Arn]    # Reference to Cognito User Pool ARN
        - Effect: Allow                       # Allow Step Functions execution
          Action:
            - states:StartExecution           # Start state machine execution
          Resource:
            - !Sub 'arn:aws:states:${AWS::Region}:${AWS::AccountId}:stateMachine:${self:service}-${self:provider.stage}-analysis'

# Custom configuration variables
custom:
  s3Bucket: ${self:service}-${self:provider.stage}-files  # S3 bucket naming convention
  serverless-offline:                         # Local development configuration
    httpPort: 3001                            # Port for HTTP API simulation
    websocketPort: 3003                       # Port for WebSocket simulation
  dotenv:                                     # Environment file configuration
    path: .env                                # Load environment variables from .env file

# Serverless Framework plugins
plugins:
  - serverless-offline                        # Local development server
  - serverless-dotenv-plugin                  # Load environment variables from .env

# Lambda functions definition
functions:
  # Authentication Functions
  login:                                      # User login function
    handler: auth/handlers.login              # Handler for local development
    image:
      name: biormika-lambda                   # Reference to ECR image
      command:
        - auth/handlers.login                 # Python function handler
    events:
      - http:                                 # HTTP API Gateway trigger
          path: auth/login                    # API endpoint path
          method: post                        # HTTP method
          cors: true                          # Enable CORS

  signup:                                     # User registration function
    handler: auth/handlers.signup             # Handler for local development
    image:
      name: biormika-lambda                   # Reference to ECR image
      command:
        - auth/handlers.signup                # Python function handler
    events:
      - http:                                 # HTTP API Gateway trigger
          path: auth/signup                   # API endpoint path
          method: post                        # HTTP method
          cors: true                          # Enable CORS

  refresh:                                    # Token refresh function
    handler: auth/handlers.refresh_token      # Handler for local development
    image:
      name: biormika-lambda                   # Reference to ECR image
      command:
        - auth/handlers.refresh_token         # Python function handler
    events:
      - http:                                 # HTTP API Gateway trigger
          path: auth/refresh                  # API endpoint path
          method: post                        # HTTP method
          cors: true                          # Enable CORS

  # File Management Functions
  initUpload:                                 # Initialize file upload function
    handler: files/handlers.init_upload      # Handler for local development
    image:
      name: biormika-lambda                   # Reference to ECR image
      command:
        - files/handlers.init_upload          # Python function handler
    events:
      - http:                                 # HTTP API Gateway trigger
          path: files/upload/init             # API endpoint path
          method: post                        # HTTP method
          cors: true                          # Enable CORS
          authorizer:                         # Require authentication
            name: authorizer                  # Authorizer name
            type: COGNITO_USER_POOLS          # Use Cognito for auth
            arn:                              # Cognito User Pool ARN
              Fn::GetAtt:
                - CognitoUserPool
                - Arn

  validateFile:                               # File validation function
    image:
      name: biormika-lambda                   # Reference to ECR image
      command:
        - files/handlers.validate_file        # Python function handler
    events:
      - http:                                 # HTTP API Gateway trigger
          path: files/validate/{fileId}       # API endpoint with path parameter
          method: post                        # HTTP method
          cors: true                          # Enable CORS
          authorizer:                         # Require authentication
            name: authorizer                  # Authorizer name
            type: COGNITO_USER_POOLS          # Use Cognito for auth
            arn:                              # Cognito User Pool ARN
              Fn::GetAtt:
                - CognitoUserPool
                - Arn

  # Analysis Functions
  startAnalysis:                              # Start HFO analysis function
    image:
      name: biormika-lambda                   # Reference to ECR image
      command:
        - analysis/handlers.start_analysis    # Python function handler
    timeout: 30                               # Function timeout in seconds
    events:
      - http:                                 # HTTP API Gateway trigger
          path: analysis/start                # API endpoint path
          method: post                        # HTTP method
          cors: true                          # Enable CORS
          authorizer:                         # Require authentication
            name: authorizer                  # Authorizer name
            type: COGNITO_USER_POOLS          # Use Cognito for auth
            arn:                              # Cognito User Pool ARN
              Fn::GetAtt:
                - CognitoUserPool
                - Arn

  getAnalysisStatus:                          # Get analysis status function
    image:
      name: biormika-lambda                   # Reference to ECR image
      command:
        - analysis/handlers.get_status        # Python function handler
    events:
      - http:                                 # HTTP API Gateway trigger
          path: analysis/status/{analysisId}  # API endpoint with path parameter
          method: get                         # HTTP method
          cors: true                          # Enable CORS
          authorizer:                         # Require authentication
            name: authorizer                  # Authorizer name
            type: COGNITO_USER_POOLS          # Use Cognito for auth
            arn:                              # Cognito User Pool ARN
              Fn::GetAtt:
                - CognitoUserPool
                - Arn

  getAnalysisResults:                         # Get analysis results function
    image:
      name: biormika-lambda                   # Reference to ECR image
      command:
        - analysis/handlers.get_results       # Python function handler
    events:
      - http:                                 # HTTP API Gateway trigger
          path: analysis/results/{analysisId} # API endpoint with path parameter
          method: get                         # HTTP method
          cors: true                          # Enable CORS
          authorizer:                         # Require authentication
            name: authorizer                  # Authorizer name
            type: COGNITO_USER_POOLS          # Use Cognito for auth
            arn:                              # Cognito User Pool ARN
              Fn::GetAtt:
                - CognitoUserPool
                - Arn

  # Step Function Tasks (for analysis workflow)
  readEdfFile:                                # Read EDF (EEG data) file function
    image:
      name: biormika-lambda                   # Reference to ECR image
      command:
        - analysis/tasks.read_edf_file        # Python function handler
    timeout: 300                              # 5 minute timeout for file processing
    memorySize: 3008                          # High memory for large file processing

  detectHfo:                                  # Detect HFO (High Frequency Oscillations) function
    image:
      name: biormika-lambda                   # Reference to ECR image
      command:
        - analysis/tasks.detect_hfo           # Python function handler
    timeout: 900                              # 15 minute timeout for complex analysis
    memorySize: 3008                          # High memory for signal processing

  generateVisualization:                      # Generate analysis visualization function
    image:
      name: biormika-lambda                   # Reference to ECR image
      command:
        - analysis/tasks.generate_visualization # Python function handler
    timeout: 300                              # 5 minute timeout for visualization
    memorySize: 3008                          # High memory for plot generation

  processResults:                             # Process and save analysis results function
    image:
      name: biormika-lambda                   # Reference to ECR image
      command:
        - analysis/tasks.process_results      # Python function handler
    timeout: 60                               # 1 minute timeout for result processing

  # WebSocket Functions (for real-time updates)
  websocketConnect:                           # WebSocket connection handler
    image:
      name: biormika-lambda                   # Reference to ECR image
      command:
        - websocket/handlers.connect          # Python function handler
    events:
      - websocket:                            # WebSocket API Gateway trigger
          route: $connect                     # Connection event

  websocketDisconnect:                        # WebSocket disconnection handler
    image:
      name: biormika-lambda                   # Reference to ECR image
      command:
        - websocket/handlers.disconnect       # Python function handler
    events:
      - websocket:                            # WebSocket API Gateway trigger
          route: $disconnect                  # Disconnection event

  websocketDefault:                           # WebSocket default message handler
    image:
      name: biormika-lambda                   # Reference to ECR image
      command:
        - websocket/handlers.default          # Python function handler
    events:
      - websocket:                            # WebSocket API Gateway trigger
          route: $default                     # Default message event

# AWS CloudFormation resources
resources:
  Resources:
    # S3 Bucket for file storage
    S3Bucket:
      Type: AWS::S3::Bucket                   # CloudFormation resource type
      Properties:
        BucketName: ${self:custom.s3Bucket}   # Bucket name from custom variables
        CorsConfiguration:                    # Cross-Origin Resource Sharing config
          CorsRules:
            - AllowedHeaders: ['*']           # Allow all headers
              AllowedMethods: [GET, PUT, POST, DELETE, HEAD] # Allow these HTTP methods
              AllowedOrigins: ['*']           # Allow all origins (for development)
              MaxAge: 3000                    # Cache preflight requests for 3000 seconds

    # DynamoDB table for data storage
    DynamoDBTable:
      Type: AWS::DynamoDB::Table              # CloudFormation resource type
      Properties:
        TableName: ${self:service}-${self:provider.stage} # Table name
        AttributeDefinitions:                 # Define table attributes
          - AttributeName: PK                 # Primary Key
            AttributeType: S                  # String type
          - AttributeName: SK                 # Sort Key
            AttributeType: S                  # String type
        KeySchema:                            # Primary key schema
          - AttributeName: PK                 # Partition key
            KeyType: HASH                     # Hash key type
          - AttributeName: SK                 # Sort key
            KeyType: RANGE                    # Range key type
        BillingMode: PAY_PER_REQUEST          # Use pay-per-request billing mode

    # Cognito User Pool for authentication
    CognitoUserPool:
      Type: AWS::Cognito::UserPool            # CloudFormation resource type
      Properties:
        UserPoolName: ${self:service}-${self:provider.stage}-user-pool # User pool name
        Schema:                               # User attributes schema
          - Name: email                       # Email attribute
            AttributeDataType: String         # String data type
            Mutable: true                     # Can be changed after creation
            Required: true                    # Required for registration
          - Name: name                        # Name attribute
            AttributeDataType: String         # String data type
            Mutable: true                     # Can be changed after creation
            Required: true                    # Required for registration
        Policies:                             # User pool policies
          PasswordPolicy:                     # Password requirements
            MinimumLength: 8                  # Minimum 8 characters
            RequireUppercase: true            # Require uppercase letter
            RequireLowercase: true            # Require lowercase letter
            RequireNumbers: true              # Require number
            RequireSymbols: false             # Don't require symbols
        AutoVerifiedAttributes:               # Auto-verify these attributes
          - email                             # Verify email addresses
        UsernameAttributes:                   # Use these as username
          - email                             # Allow email as username
        MfaConfiguration: OFF                 # Multi-factor authentication disabled

    # Cognito User Pool Client for app authentication
    CognitoUserPoolClient:
      Type: AWS::Cognito::UserPoolClient      # CloudFormation resource type
      Properties:
        ClientName: ${self:service}-${self:provider.stage}-client # Client name
        GenerateSecret: false                 # Don't generate client secret (for public apps)
        UserPoolId:                           # Associated user pool
          Ref: CognitoUserPool                # Reference to user pool
        ExplicitAuthFlows:                    # Allowed authentication flows
          - ALLOW_USER_PASSWORD_AUTH          # Username/password authentication
          - ALLOW_ADMIN_USER_PASSWORD_AUTH    # Admin username/password authentication
          - ALLOW_REFRESH_TOKEN_AUTH          # Refresh token authentication
        SupportedIdentityProviders:           # Supported identity providers
          - COGNITO                           # Built-in Cognito provider
        AllowedOAuthFlows:                    # OAuth 2.0 flows
          - code                              # Authorization code flow
          - implicit                          # Implicit flow
        AllowedOAuthScopes:                   # OAuth scopes
          - email                             # Email scope
          - openid                            # OpenID Connect scope
          - profile                           # Profile scope
        CallbackURLs:                         # Allowed callback URLs
          - http://localhost:3000/callback    # Local development URL
          - https://app.biormika.com/callback # Production URL

    # Step Functions State Machine for analysis workflow
    AnalysisStateMachine:
      Type: AWS::StepFunctions::StateMachine  # CloudFormation resource type
      Properties:
        StateMachineName: ${self:service}-${self:provider.stage}-analysis # State machine name
        RoleArn:                              # Execution role ARN
          Fn::GetAtt: [StatesExecutionRole, Arn] # Reference to execution role
        DefinitionString:                     # State machine definition (JSON as string)
          Fn::Sub: |                          # CloudFormation substitution
            {
              "Comment": "HFO Analysis Workflow",
              "StartAt": "ReadEdfFile",
              "States": {
                "ReadEdfFile": {
                  "Type": "Task",
                  "Resource": "${ReadEdfFileLambdaFunction.Arn}",
                  "Next": "DetectHfo",
                  "Retry": [
                    {
                      "ErrorEquals": ["States.TaskFailed"],
                      "IntervalSeconds": 2,
                      "MaxAttempts": 3,
                      "BackoffRate": 2
                    }
                  ]
                },
                "DetectHfo": {
                  "Type": "Task",
                  "Resource": "${DetectHfoLambdaFunction.Arn}",
                  "Next": "GenerateVisualization",
                  "Retry": [
                    {
                      "ErrorEquals": ["States.TaskFailed"],
                      "IntervalSeconds": 2,
                      "MaxAttempts": 3,
                      "BackoffRate": 2
                    }
                  ]
                },
                "GenerateVisualization": {
                  "Type": "Task",
                  "Resource": "${GenerateVisualizationLambdaFunction.Arn}",
                  "Next": "ProcessResults",
                  "Retry": [
                    {
                      "ErrorEquals": ["States.TaskFailed"],
                      "IntervalSeconds": 2,
                      "MaxAttempts": 3,
                      "BackoffRate": 2
                    }
                  ]
                },
                "ProcessResults": {
                  "Type": "Task",
                  "Resource": "${ProcessResultsLambdaFunction.Arn}",
                  "End": true
                }
              }
            }

    # IAM role for Step Functions execution
    StatesExecutionRole:
      Type: AWS::IAM::Role                    # CloudFormation resource type
      Properties:
        AssumeRolePolicyDocument:             # Trust policy
          Version: '2012-10-17'              # Policy version
          Statement:
            - Effect: Allow                   # Allow assumption
              Principal:
                Service:
                  - states.amazonaws.com      # Step Functions service
              Action: sts:AssumeRole          # Assume role action
        Policies:                             # Attached policies
          - PolicyName: StatesExecutionPolicy # Policy name
            PolicyDocument:                   # Policy document
              Version: '2012-10-17'          # Policy version
              Statement:
                - Effect: Allow               # Allow Lambda invocation
                  Action:
                    - lambda:InvokeFunction   # Invoke Lambda functions
                  Resource:                   # Target Lambda function ARNs
                    - Fn::GetAtt: [ReadEdfFileLambdaFunction, Arn]
                    - Fn::GetAtt: [DetectHfoLambdaFunction, Arn]
                    - Fn::GetAtt: [GenerateVisualizationLambdaFunction, Arn]
                    - Fn::GetAtt: [ProcessResultsLambdaFunction, Arn]

  # CloudFormation outputs
  Outputs:
    UserPoolId:                               # Cognito User Pool ID output
      Value:
        Ref: CognitoUserPool                  # Reference to user pool
    UserPoolClientId:                         # Cognito User Pool Client ID output
      Value:
        Ref: CognitoUserPoolClient            # Reference to user pool client
    S3BucketName:                             # S3 bucket name output
      Value:
        Ref: S3Bucket                         # Reference to S3 bucket
    DynamoDBTableName:                        # DynamoDB table name output
      Value:
        Ref: DynamoDBTable                    # Reference to DynamoDB table
    StateMachineArn:                          # Step Functions state machine ARN output
      Value:
        Ref: AnalysisStateMachine             # Reference to state machine