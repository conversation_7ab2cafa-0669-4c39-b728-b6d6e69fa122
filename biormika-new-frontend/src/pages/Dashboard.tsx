import { useState, useEffect, useContext } from 'react'
import { useNavigate } from 'react-router-dom'
import { toast } from 'sonner'
import { FileList } from '../components/FileList'
import { FileUploadArea } from '../components/FileUploadArea'
import { LoadingSpinner } from '../components/LoadingSpinner'
import { PageHeader } from '../components/PageHeader'
import { PageLayout } from '../components/PageLayout'
import { SettingsSidebar } from '../components/SettingsSidebar'
import { useAppDispatch, useAppSelector } from '../store'
import { clearAllFiles, updateFileStatus } from '../store/slices/filesSlice'
import { FileStatus } from '../types/files'
import { useFileUpload } from '../hooks/useFileUpload'
import { checkDuplicateFiles } from '../utils/fileValidation'
import { WebSocketContext } from '../contexts/WebSocketContext'
import type { FileValidatedMessage } from '../services/websocket.service'

export default function Dashboard() {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [selectedFileId, setSelectedFileId] = useState<string>('')
  const [selectedFileName, setSelectedFileName] = useState<string>('')

  const dispatch = useAppDispatch()
  const navigate = useNavigate()
  const { files, isUploading } = useAppSelector((state) => state.files)
  const { isConfigured } = useAppSelector((state) => state.analysisSettings)
  const hasFiles = files.length > 0
  const websocket = useContext(WebSocketContext)

  const { addFiles } = useFileUpload({
    onSuccess: () => {
      toast.success('File uploaded and validated successfully!')
    },
    onError: (error) => {
      toast.error(`Upload failed: ${error.message}`)
    },
  })

  // Subscribe to WebSocket file validation events
  useEffect(() => {
    if (!websocket) return

    const handleFileValidated = (data: FileValidatedMessage) => {
      console.log('WebSocket: File validated event', data)

      // Find the file by serverFileId and update its status
      const file = files.find((f) => f.serverFileId === data.fileId)
      if (file) {
        dispatch(
          updateFileStatus({
            id: file.id,
            status: data.status === 'valid' ? FileStatus.VALID : FileStatus.ERROR,
            errorMessage: data.error,
          })
        )

        if (data.status === 'valid') {
          toast.success(`File ${file.name} validated successfully`)
        } else {
          toast.error(`File ${file.name} validation failed: ${data.error || 'Unknown error'}`)
        }
      }
    }

    const handleFileError = (data: { fileId: string; error: string }) => {
      console.log('WebSocket: File error event', data)

      const file = files.find((f) => f.serverFileId === data.fileId)
      if (file) {
        dispatch(
          updateFileStatus({
            id: file.id,
            status: FileStatus.ERROR,
            errorMessage: data.error || 'File processing error',
          })
        )
        toast.error(`File ${file.name} error: ${data.error || 'Unknown error'}`)
      }
    }

    const unsubscribeValidated = websocket.subscribeToFileValidated(handleFileValidated)
    const unsubscribeError = websocket.subscribeToFileError(handleFileError)

    return () => {
      unsubscribeValidated()
      unsubscribeError()
    }
  }, [websocket, files, dispatch])

  const handleReUpload = () => {
    dispatch(clearAllFiles())
  }

  const handleAddMoreFiles = () => {
    // Trigger the file browser directly instead of navigation
    document.getElementById('file-input')?.click()
  }

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const fileList = Array.from(e.target.files || [])
    if (fileList.length > 0) {
      // Check for duplicates
      const { duplicates, unique } = checkDuplicateFiles(fileList, files)

      if (unique.length > 0) {
        // Use the real file upload service
        addFiles(unique)

        // Show success toast
        let message = `${unique.length} file${unique.length > 1 ? 's' : ''} added for upload`
        if (duplicates.length > 0) {
          message += `, ${duplicates.length} duplicate${duplicates.length > 1 ? 's' : ''} skipped`
        }
        toast.success(message)
      } else if (duplicates.length > 0) {
        // Only duplicates were selected
        toast.error(
          `${duplicates.length} duplicate file${duplicates.length > 1 ? 's' : ''} skipped`
        )
      }
    }

    // Clear the input value so the same file can be selected again
    e.target.value = ''
  }

  const handleContinue = () => {
    if (isConfigured) {
      navigate('/analysis')
    } else {
      navigate('/configuration')
    }
  }

  const handleConfigure = (fileId: string) => {
    const file = files.find((f) => f.id === fileId)
    if (file) {
      setSelectedFileId(fileId)
      setSelectedFileName(file.name)
      setSidebarOpen(true)
    }
  }

  const handleCloseSidebar = () => {
    setSidebarOpen(false)
    setSelectedFileId('')
    setSelectedFileName('')
  }

  const allFilesProcessed = files.every(
    (file) => file.status === FileStatus.VALID || file.status === FileStatus.ERROR
  )

  // Show initial upload screen when no files
  if (!hasFiles) {
    return (
      <PageLayout showFooter>
        <PageHeader title="Biormika" subtitle="Smarter Brainwave Analysis" variant="brand" />
        <FileUploadArea />
      </PageLayout>
    )
  }

  // Show processing screen when files are being uploaded
  if (isUploading) {
    return (
      <PageLayout>
        <div className="w-full max-w-4xl">
          <PageHeader
            title="Processing Files"
            subtitle="This may take a few minutes"
            variant="processing"
          >
            <LoadingSpinner />
          </PageHeader>

          <FileList showActions={false} hideHeader={true} onConfigure={handleConfigure} />
        </div>
      </PageLayout>
    )
  }

  // Show completed file management screen
  return (
    <>
      <PageLayout showFooter>
        <div className="w-full max-w-4xl">
          {/* Hidden file input for Add More Files functionality */}
          <input
            id="file-input"
            type="file"
            accept=".edf"
            multiple
            onChange={handleFileSelect}
            className="hidden"
          />

          <FileList
            showActions={true}
            hideHeader={false}
            onReUpload={allFilesProcessed ? handleReUpload : undefined}
            onAddMoreFiles={allFilesProcessed ? handleAddMoreFiles : undefined}
            onContinue={allFilesProcessed ? handleContinue : undefined}
            onConfigure={handleConfigure}
          />
        </div>
      </PageLayout>

      <SettingsSidebar
        isOpen={sidebarOpen}
        onClose={handleCloseSidebar}
        fileId={selectedFileId}
        fileName={selectedFileName}
      />
    </>
  )
}
