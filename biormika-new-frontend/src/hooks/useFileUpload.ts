import { useCallback, useState, useEffect, useRef } from 'react'
import { useAppDispatch, useAppSelector } from '../store'
import {
  addFiles,
  removeFile,
  updateFileStatus,
  updateFileProgress,
  setIsUploading,
  clearAllFiles,
} from '../store/slices/filesSlice'
import { filesService } from '../services/files.service'
import type { FileUpload } from '../types/files'
import { FileStatus } from '../types/files'
import { showToast } from '../utils/toast'
import { fileStorageManager } from '../utils/fileStorageManager'

interface UseFileUploadOptions {
  onSuccess?: (fileId: string) => void
  onError?: (error: Error, fileId: string) => void
  maxConcurrentUploads?: number
}

export function useFileUpload(options: UseFileUploadOptions = {}) {
  const dispatch = useAppDispatch()
  const { files, isUploading } = useAppSelector((state) => state.files)
  const [uploadQueue, setUploadQueue] = useState<Set<string>>(new Set())
  const { maxConcurrentUploads = 3 } = options

  // Use ref only for processing state
  const isProcessingRef = useRef(false)

  const uploadFile = useCallback(
    async (file: FileUpload) => {
      // Get the actual File object from our storage
      fileStorageManager.logState('uploadFile start')
      const fileObject = fileStorageManager.get(file.id)
      if (!fileObject) {
        console.error('No file object found for upload:', file.id)
        fileStorageManager.logState('uploadFile error')
        // Update file status to error
        dispatch(
          updateFileStatus({
            id: file.id,
            status: FileStatus.ERROR,
            errorMessage: 'File data not available. Please re-upload the file.',
          })
        )
        return
      }

      console.log('Starting upload for file:', file.name, 'ID:', file.id)
      dispatch(updateFileStatus({ id: file.id, status: FileStatus.UPLOADING }))
      setUploadQueue((prev) => new Set(prev).add(file.id))

      try {
        const result = await filesService.uploadFile(fileObject, (progress) => {
          console.log(`Upload progress for ${file.name}:`, progress.progress + '%')
          dispatch(
            updateFileProgress({
              id: file.id,
              progress: progress.progress,
            })
          )
        })

        console.log('Upload result for', file.name, ':', result)
        dispatch(
          updateFileStatus({
            id: file.id,
            status: result.status === 'valid' ? FileStatus.VALID : FileStatus.ERROR,
            errorMessage: result.status !== 'valid' ? 'File validation failed' : undefined,
            serverFileId: result.fileId,
          })
        )

        if (result.status === 'valid' && options.onSuccess) {
          options.onSuccess(result.fileId)
        }

        // Clean up the file from storage after successful upload
        fileStorageManager.delete(file.id)
      } catch (error) {
        console.error('Upload error for', file.name, ':', error)
        const errorMessage = error instanceof Error ? error.message : 'Upload failed'
        dispatch(
          updateFileStatus({
            id: file.id,
            status: FileStatus.ERROR,
            errorMessage,
          })
        )

        if (options.onError) {
          options.onError(error instanceof Error ? error : new Error(String(error)), file.id)
        }
      } finally {
        setUploadQueue((prev) => {
          const newSet = new Set(prev)
          newSet.delete(file.id)
          return newSet
        })
      }
    },
    [dispatch, options]
  )

  const processUploadQueue = useCallback(async () => {
    // Prevent concurrent processing
    if (isProcessingRef.current) {
      console.log('Already processing queue, skipping')
      return
    }

    isProcessingRef.current = true

    try {
      console.log('Processing upload queue, total files:', files.length)
      const pendingFiles = files.filter((f) => f.status === FileStatus.PENDING)
      const currentlyUploading = uploadQueue.size

      console.log('Pending files:', pendingFiles.length, 'Currently uploading:', currentlyUploading)

      if (pendingFiles.length === 0 || currentlyUploading >= maxConcurrentUploads) {
        if (pendingFiles.length === 0 && currentlyUploading === 0) {
          console.log('No files to upload, setting isUploading to false')
          dispatch(setIsUploading(false))
        }
        return
      }

      console.log('Starting upload process')
      dispatch(setIsUploading(true))

      const filesToUpload = pendingFiles.slice(0, maxConcurrentUploads - currentlyUploading)
      console.log('Files to upload in this batch:', filesToUpload.length)

      await Promise.all(filesToUpload.map((file) => uploadFile(file)))

      // Process next batch immediately
      isProcessingRef.current = false

      // Small delay to allow state updates
      setTimeout(() => {
        // Re-check files from Redux state to get updated statuses
        const updatedFiles = files.filter((f) => f.status === FileStatus.PENDING)
        if (updatedFiles.length > 0) {
          console.log('Still have pending files, continuing processing')
          processUploadQueue()
        } else {
          console.log('No more pending files, stopping processing')
        }
      }, 100)
    } catch (error) {
      console.error('Error in processUploadQueue:', error)
      isProcessingRef.current = false
    }
  }, [files, uploadQueue, maxConcurrentUploads, dispatch, uploadFile])

  const validateFiles = useCallback((fileList: File[]): File[] => {
    const validFiles: File[] = []

    fileList.forEach((file) => {
      const error = filesService.getFileValidationError(file)
      if (error) {
        showToast.error(`${file.name}: ${error}`)
      } else {
        validFiles.push(file)
      }
    })

    return validFiles
  }, [])

  const addFilesToQueue = useCallback(
    (fileList: File[]) => {
      console.log('Adding files to queue:', fileList.length)
      const validFiles = validateFiles(fileList)

      if (validFiles.length === 0) {
        console.log('No valid files to add')
        return
      }

      console.log('Valid files to upload:', validFiles.length)

      // Create file objects with IDs and store File objects
      const filesWithIds = validFiles.map((file, index) => {
        const id = `${Date.now()}-${index}-${Math.random().toString(36).substring(2, 9)}`
        console.log('Creating file with ID:', id, 'Name:', file.name)
        // Store the actual File object in global storage
        fileStorageManager.set(id, file)
        return { id, file }
      })

      fileStorageManager.logState('after adding files')

      // Add files to Redux
      dispatch(addFiles({ files: filesWithIds }))

      // Don't process here - let the useEffect handle it
      console.log('Files added to Redux, waiting for state update to trigger processing')
    },
    [dispatch, validateFiles]
  )

  const cancelUpload = useCallback(
    (fileId: string) => {
      filesService.cancelUpload(fileId)
      dispatch(removeFile(fileId))
      setUploadQueue((prev) => {
        const newSet = new Set(prev)
        newSet.delete(fileId)
        return newSet
      })
      // Clean up file storage
      fileStorageManager.delete(fileId)
    },
    [dispatch]
  )

  const retryUpload = useCallback(
    (fileId: string) => {
      const file = files.find((f) => f.id === fileId)
      if (!file) return

      // Check if we still have the File object
      if (!fileStorageManager.has(fileId)) {
        showToast.error(
          'Cannot retry upload: File data is no longer available. Please re-upload the file.'
        )
        dispatch(removeFile(fileId))
        return
      }

      dispatch(updateFileStatus({ id: fileId, status: FileStatus.PENDING }))
      dispatch(updateFileProgress({ id: fileId, progress: 0 }))

      if (!isUploading) {
        processUploadQueue()
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [files, dispatch, isUploading]
  )

  const clearFiles = useCallback(() => {
    uploadQueue.forEach((fileId) => {
      filesService.cancelUpload(fileId)
    })
    dispatch(clearAllFiles())
    setUploadQueue(new Set())
    // Clear file storage
    fileStorageManager.clear()
  }, [dispatch, uploadQueue])

  const deleteFile = useCallback(
    async (fileId: string, serverFileId?: string) => {
      try {
        if (serverFileId) {
          await filesService.deleteFile(serverFileId)
        }
        dispatch(removeFile(fileId))
        // Clean up file storage
        fileStorageManager.delete(fileId)
        showToast.success('File deleted successfully')
      } catch {
        showToast.error('Failed to delete file')
      }
    },
    [dispatch]
  )

  // Monitor files and trigger uploads
  useEffect(() => {
    const hasPendingFiles = files.some((f) => f.status === FileStatus.PENDING)
    const hasUploadingFiles = files.some((f) => f.status === FileStatus.UPLOADING)

    console.log('useEffect - File status check:', {
      hasPendingFiles,
      hasUploadingFiles,
      isProcessing: isProcessingRef.current,
      filesCount: files.length,
      uploadQueueSize: uploadQueue.size,
    })

    // Use a small delay to debounce rapid state changes
    const timeoutId = setTimeout(() => {
      // Trigger upload processing when there are pending files
      if (hasPendingFiles && !isProcessingRef.current && uploadQueue.size < maxConcurrentUploads) {
        console.log('Found pending files, triggering processUploadQueue')
        fileStorageManager.logState('useEffect before processUploadQueue')
        processUploadQueue()
      }

      // Update isUploading state when all uploads are complete
      if (!hasPendingFiles && !hasUploadingFiles && uploadQueue.size === 0 && isUploading) {
        console.log('All uploads complete, setting isUploading to false')
        dispatch(setIsUploading(false))
      }
    }, 50)

    return () => clearTimeout(timeoutId)
  }, [files, uploadQueue.size, isUploading, dispatch, processUploadQueue, maxConcurrentUploads])

  // Clean up file storage on unmount
  useEffect(() => {
    return () => {
      console.log('useFileUpload unmounting, clearing file storage')
      fileStorageManager.clear()
    }
  }, [])

  return {
    files,
    isUploading,
    uploadingCount: uploadQueue.size,
    addFiles: addFilesToQueue,
    cancelUpload,
    retryUpload,
    clearFiles,
    deleteFile,
    processQueue: processUploadQueue,
  }
}

export function useFileList() {
  const [files, setFiles] = useState<
    Array<{ id: string; name: string; size: number; uploadedAt: string; status: string }>
  >([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [page, setPage] = useState(1)
  const [total, setTotal] = useState(0)

  const fetchFiles = useCallback(async (pageNum = 1) => {
    setIsLoading(true)
    setError(null)

    try {
      const result = await filesService.listFiles(pageNum)
      setFiles(result.files)
      setTotal(result.total)
      setPage(pageNum)
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to fetch files')
    } finally {
      setIsLoading(false)
    }
  }, [])

  return {
    files,
    isLoading,
    error,
    page,
    total,
    fetchFiles,
    refresh: () => fetchFiles(page),
  }
}
