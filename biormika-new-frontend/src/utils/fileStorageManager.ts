/**
 * Global File storage manager to ensure File objects are accessible
 * across all instances of useFileUpload hook
 */
class FileStorageManager {
  private storage: Map<string, File>

  constructor() {
    this.storage = new Map()
  }

  /**
   * Add a File object to storage
   */
  set(id: string, file: File): void {
    console.log(`[FileStorageManager] Storing file with ID: ${id}, Name: ${file.name}`)
    this.storage.set(id, file)
  }

  /**
   * Get a File object from storage
   */
  get(id: string): File | undefined {
    const file = this.storage.get(id)
    if (!file) {
      console.log(`[FileStorageManager] No file found for ID: ${id}, Available IDs: ${Array.from(this.storage.keys()).join(', ')}`)
    }
    return file
  }

  /**
   * Check if a File object exists in storage
   */
  has(id: string): boolean {
    return this.storage.has(id)
  }

  /**
   * Remove a File object from storage
   */
  delete(id: string): boolean {
    console.log(`[FileStorageManager] Deleting file with ID: ${id}`)
    return this.storage.delete(id)
  }

  /**
   * Clear all File objects from storage
   */
  clear(): void {
    console.log(`[FileStorageManager] Clearing all files (count: ${this.storage.size})`)
    this.storage.clear()
  }

  /**
   * Get all stored file IDs
   */
  getIds(): string[] {
    return Array.from(this.storage.keys())
  }

  /**
   * Get the number of stored files
   */
  size(): number {
    return this.storage.size
  }

  /**
   * Debug helper to log current storage state
   */
  logState(context: string): void {
    console.log(`[FileStorageManager - ${context}] File count: ${this.storage.size}, IDs: ${this.getIds().join(', ')}`)
  }
}

// Create a singleton instance
export const fileStorageManager = new FileStorageManager()