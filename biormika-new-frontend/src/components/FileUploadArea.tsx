import { useState } from 'react'
import { Upload, CheckCircle, X, AlertCircle, Loader2 } from 'lucide-react'
import { useFileUpload } from '../hooks/useFileUpload'
import { FILE_UPLOAD_CONFIG } from '../constants/api'
import { brandColors } from '../constants/colors'
import { FileStatus } from '../types/files'

export function FileUploadArea() {
  const [isDragOver, setIsDragOver] = useState(false)
  const { files, addFiles, cancelUpload, retryUpload } = useFileUpload()

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
  }

  const handleFilesUpload = (fileList: File[]) => {
    addFiles(fileList)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
    const fileList = Array.from(e.dataTransfer.files)
    handleFilesUpload(fileList)
  }

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const fileList = Array.from(e.target.files || [])
    handleFilesUpload(fileList)
    e.target.value = ''
  }

  const handleBrowseClick = () => {
    document.getElementById('file-input')?.click()
  }

  return (
    <div className="w-full max-w-4xl mx-auto">
      <div
        className={`
          relative border-2 border-dashed rounded-lg p-16 text-center transition-colors
          ${isDragOver ? 'border-blue-400 bg-blue-50' : 'border-gray-300 bg-white'}
        `}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <input
          id="file-input"
          type="file"
          accept=".edf"
          multiple
          onChange={handleFileSelect}
          className="hidden"
        />
        
        <div className="flex flex-col items-center gap-4">
          <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
            <Upload className="w-6 h-6 text-blue-600" />
          </div>
          
          <div className="space-y-2">
            <p className="text-gray-700 font-medium">
              {files.length > 0 ? 'Add more files by dragging & dropping or' : 'Drag & drop files or'}{' '}
              <button
                onClick={handleBrowseClick}
                className="text-blue-600 hover:text-blue-700 underline font-medium"
              >
                Browse
              </button>
            </p>
            {files.length > 0 && (
              <p className="text-sm text-blue-600">
                Adding to {files.length} existing file{files.length > 1 ? 's' : ''}
              </p>
            )}
          </div>
        </div>
      </div>
      
      <div className="mt-4 text-center">
        <p className="text-gray-600 text-sm leading-relaxed">
          File size max {FILE_UPLOAD_CONFIG.maxSize / (1024 * 1024)}MB, format EDF (European Data Format) only,<br />
          file must be redacted with no patient-identifiable information.
        </p>
      </div>

      {/* Show existing files when files exist */}
      {files.length > 0 && (
        <div className="mt-8">
          <h3 className="text-lg font-semibold mb-4" style={{ color: brandColors.text.primary }}>
            Files ({files.length})
          </h3>
          <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
            {files.map((file, index) => (
              <div
                key={file.id}
                className={`flex items-center justify-between px-4 py-3 ${
                  index !== files.length - 1 ? 'border-b border-gray-100' : ''
                }`}
              >
                <div className="flex items-center gap-3 min-w-0 flex-1">
                  <div className="flex-shrink-0">
                    {file.status === FileStatus.UPLOADING && (
                      <Loader2 className="w-5 h-5 animate-spin" style={{ color: brandColors.primary }} />
                    )}
                    {file.status === FileStatus.VALID && (
                      <CheckCircle className="w-5 h-5" style={{ color: brandColors.success }} />
                    )}
                    {file.status === FileStatus.ERROR && (
                      <AlertCircle className="w-5 h-5" style={{ color: brandColors.error }} />
                    )}
                    {file.status === FileStatus.PENDING && (
                      <div className="w-5 h-5 rounded-full bg-gray-200" />
                    )}
                  </div>
                  <div className="min-w-0 flex-1">
                    <p 
                      className="font-medium text-sm truncate" 
                      style={{ color: brandColors.text.primary }}
                      title={file.name}
                    >
                      {file.name}
                    </p>
                    <div className="flex items-center gap-2">
                      <p className="text-xs" style={{ color: brandColors.text.secondary }}>
                        {formatFileSize(file.size)}
                      </p>
                      {file.status === FileStatus.UPLOADING && file.uploadProgress > 0 && (
                        <span className="text-xs" style={{ color: brandColors.primary }}>
                          {file.uploadProgress}%
                        </span>
                      )}
                      {file.errorMessage && (
                        <span className="text-xs" style={{ color: brandColors.error }}>
                          {file.errorMessage}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {file.status === FileStatus.ERROR && (
                    <button
                      onClick={() => retryUpload(file.id)}
                      className="text-xs px-2 py-1 rounded hover:bg-gray-100"
                      style={{ color: brandColors.primary }}
                    >
                      Retry
                    </button>
                  )}
                  {(file.status === FileStatus.UPLOADING || file.status === FileStatus.PENDING) && (
                    <button
                      onClick={() => cancelUpload(file.id)}
                      className="p-1 rounded hover:bg-gray-100"
                    >
                      <X className="w-4 h-4" style={{ color: brandColors.text.secondary }} />
                    </button>
                  )}
                  <span 
                    className="text-xs px-2 py-1 rounded-full" 
                    style={{ 
                      backgroundColor: getStatusColor(file.status).bg,
                      color: getStatusColor(file.status).text
                    }}
                  >
                    {getStatusLabel(file.status)}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

function getStatusColor(status: FileStatus) {
  switch (status) {
    case FileStatus.VALID:
      return { bg: `${brandColors.success}20`, text: brandColors.success }
    case FileStatus.ERROR:
      return { bg: `${brandColors.error}20`, text: brandColors.error }
    case FileStatus.UPLOADING:
    case FileStatus.VALIDATING:
      return { bg: `${brandColors.primary}20`, text: brandColors.primary }
    default:
      return { bg: '#f3f4f6', text: brandColors.text.secondary }
  }
}

function getStatusLabel(status: FileStatus) {
  switch (status) {
    case FileStatus.PENDING:
      return 'Pending'
    case FileStatus.UPLOADING:
      return 'Uploading'
    case FileStatus.VALIDATING:
      return 'Validating'
    case FileStatus.VALID:
      return 'Valid'
    case FileStatus.ERROR:
      return 'Error'
    default:
      return status
  }
}